// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: '************',
    appId: '********',
    messagingSenderId: '324152946798',
    projectId: '***-******',
    authDomain: '********.firebaseapp.com',
    storageBucket: '********',
    measurementId: 'G-NS73KE3MFR',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: '*********',
    appId: '*********',
    messagingSenderId: '******',
    projectId: '****',
    databaseURL: '-***********',
    storageBucket: '******',
    iosBundleId: 'com.fastlaundry.deliveryapp',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: '*******',
    appId: '*********',
    messagingSenderId: '********',
    projectId: '********',
    databaseURL: '*****',
    storageBucket: '*************',
  );

}