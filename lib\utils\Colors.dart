// ignore_for_file: non_constant_identifier_names, prefer_const_constructors, file_names

import 'package:flutter/material.dart';

Color WhiteColor = const Color(0xffFFFFFF);
Color BlackColor = const Color(0xff000000);
Color bgcolor = const Color(0xFFF5F5F5);
Color bg1Color = const Color(0xFFF5F4F9);
// Color greyColor = const Color(0xffA7AEC1);
Color Darkblue = const Color(0xff3D5BF6);
Color yelloColor = const Color(0xffFFBB0D);
Color RedColor = const Color(0xffFF4747);
Color lightgrey = const Color(0xffDDDDDD);
Color darkmode = const Color(0xff111315);
Color boxcolor = const Color(0xff202427);
Color greycolor = const Color(0xff9e9e9e);
Color Greycolor = const Color(0xffA7AEC1);
Color perpulshadow = const Color(0xffede3ed);
Color blueColor = const Color(0xff2196f3);
Color greenColor = const Color(0xff00ff00);
Color gradientColor = const Color(0xff00D261);
Color brownColor = const Color(0xff481f01);
Color orangeColor = const Color(0xffff9933);
Color lightyello = const Color(0xfff4c430);
Color redgradient = const Color(0xffFF6B6B);
// ignore: use_full_hex_values_for_flutter_colors
Color textcolor = const Color(0xffF071731);
// ignore: use_full_hex_values_for_flutter_colors
Color yellowshadow = const Color(0xffFfff6e9);
Color boxbgcolor = const Color(0xffE6EBF2);
Color bordercolor = const Color(0xffF5F2FB);
Color buttoncolor = const Color(0xff042628);
Color NeutralGrey = const Color(0xff666d70);
Color transparent = Colors.transparent;
Color progressColor = const Color(0xff70B9BE);
Color greytext = const Color(0xff97A2B0);
Color BlackColor2 = Color.fromARGB(255, 49, 15, 15);
Color greyColor = const Color(0xffA7AEC1);
Color greyColor2 = const Color(0xffA7AEC1);
Color Darkblue2 = const Color(0xff3D5BF6);
Color yelloColor2 = const Color(0xffFFBB0D);
Color RedColor2 = const Color(0xffFF5252);
Color lightgrey2 = const Color(0xffDDDDDD);
// ignore: use_full_hex_values_for_flutter_colors
Color lightBlack = const Color(0xff73000000);
Color lightBlack2 = const Color(0XFF636777);
Color onoffColor = const Color(0xffE7E7E7);
Color onoffColor2 = const Color(0xffE7E7E7);
